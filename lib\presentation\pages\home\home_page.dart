import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flockin_v2_app/core/theme/theme.dart';
import 'package:flockin_v2_app/presentation/widgets/common/custom_header.dart';
import 'package:flockin_v2_app/presentation/widgets/attendance/status_message_card.dart';
import 'package:flockin_v2_app/presentation/widgets/home/<USER>';
import 'package:flockin_v2_app/presentation/widgets/home/<USER>';
import 'package:flockin_v2_app/presentation/widgets/attendance/attendance_history_section.dart';
import 'package:flockin_v2_app/presentation/bloc/auth/auth.dart';

/// Home page with student attendance dashboard
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        // Default values for unauthenticated state
        String userName = 'User Name';
        List<String> userBadges = ['Member'];

        if (state is AuthAuthenticated) {
          userName = state.user.name;
          userBadges = [state.user.role, state.user.church.name];
        }

        return Scaffold(
          backgroundColor: AppColors.background,
          body: SingleChildScrollView(
            child: Column(
              children: [
                // Full-width header with integrated profile card
                CustomHeader(userName: userName, userBadges: userBadges),

                // Main content spacing
                const SizedBox(height: 24),
                // Main content with consistent padding
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 16),
                      // Status message card
                      const StatusMessageCard(),
                      const SizedBox(height: 24),
                      // Announcements button
                      const AnnouncementsButton(),
                      const SizedBox(height: 24),
                      // Presence section
                      const PresenceSection(),
                      const SizedBox(height: 24),
                      // Attendance history
                      const AttendanceHistorySection(),
                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
