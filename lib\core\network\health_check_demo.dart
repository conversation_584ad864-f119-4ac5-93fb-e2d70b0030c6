import 'package:flutter/material.dart';
import 'health_check_service.dart';
import 'network_client.dart';

class HealthCheckDemo extends StatefulWidget {
  const HealthCheckDemo({super.key});

  @override
  State<HealthCheckDemo> createState() => _HealthCheckDemoState();
}

class _HealthCheckDemoState extends State<HealthCheckDemo> {
  String _result = '';
  bool _loading = false;

  Future<void> _checkHealth() async {
    setState(() => _loading = true);
    final service = HealthCheckService(NetworkClient());
    final result = await service.checkHealth();
    setState(() {
      _result = result;
      _loading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('API Health Check')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: _loading ? null : _checkHealth,
              child: const Text('Check API Health'),
            ),
            const SizedBox(height: 24),
            if (_loading) const CircularProgressIndicator(),
            if (_result.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(_result, style: const TextStyle(fontSize: 14)),
            ],
          ],
        ),
      ),
    );
  }
}
