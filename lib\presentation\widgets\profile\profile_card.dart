import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flockin_v2_app/core/theme/theme.dart';
import 'package:flockin_v2_app/presentation/widgets/common/custom_badge.dart';
import 'package:flockin_v2_app/presentation/bloc/auth/auth.dart';

/// Profile card widget with avatar, name, badges, and edit icon
class ProfileCard extends StatelessWidget {
  const ProfileCard({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        // Default values for unauthenticated state
        String userName = 'User Name';
        List<String> userBadges = ['Member'];

        if (state is AuthAuthenticated) {
          userName = state.user.name;
          userBadges = [state.user.role, state.user.church.name];
        }

        return Card(
          elevation: 4,
          shadowColor: AppColors.black.withValues(alpha: 0.1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Profile Avatar
                Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: AppColors.lightGrey, width: 2),
                  ),
                  child: ClipOval(
                    child: Container(
                      color: AppColors.lightGrey,
                      child: const Icon(
                        Icons.person,
                        size: 32,
                        color: AppColors.grey,
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 16),

                // Name and badges
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Name
                      Text(
                        userName,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: AppColors.black,
                            ),
                      ),

                      const SizedBox(height: 8),

                      // Badges
                      Row(
                        children: userBadges
                            .map(
                              (badge) => Padding(
                                padding: const EdgeInsets.only(right: 8),
                                child: CustomBadge(text: badge),
                              ),
                            )
                            .toList(),
                      ),
                    ],
                  ),
                ),

                // Edit icon
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColors.lightGrey.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.edit,
                    size: 20,
                    color: AppColors.grey,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
