import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:flockin_v2_app/core/theme/theme.dart';
import 'package:flockin_v2_app/presentation/widgets/common/custom_header.dart';
import 'package:flockin_v2_app/presentation/widgets/profile/attendance_stats_section.dart';
import 'package:flockin_v2_app/presentation/widgets/profile/profile_main_menu.dart';
import 'package:flockin_v2_app/presentation/bloc/auth/auth.dart';

/// Profile page with attendance stats and main menu
class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthUnauthenticated) {
          // Navigate to login page when user is logged out
          context.go('/login');
        } else if (state is AuthError) {
          // Show error message but still navigate to login
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: AppColors.error,
            ),
          );
          context.go('/login');
        }
      },
      child: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, state) {
          // Default values for unauthenticated state
          String userName = 'User Name';
          List<String> userBadges = ['Member'];

          if (state is AuthAuthenticated) {
            userName = state.user.name;
            userBadges = [state.user.role, state.user.church.name];
          }

          return Scaffold(
            backgroundColor: AppColors.background,
            body: SingleChildScrollView(
              child: Column(
                children: [
                  // Full-width header with profile card
                  CustomHeader(
                    pageTitle: 'Profile',
                    userName: userName,
                    userBadges: userBadges,
                  ),

                  // Main content spacing
                  const SizedBox(height: 24),

                  // Attendance stats section
                  const AttendanceStatsSection(),

                  const SizedBox(height: 20),

                  // Main menu section
                  const ProfileMainMenuSection(),

                  // Bottom spacing
                  const SizedBox(height: 24),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
