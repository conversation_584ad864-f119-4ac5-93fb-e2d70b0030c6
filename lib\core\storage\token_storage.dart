import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flockin_v2_app/core/network/auth_models.dart';
import 'package:flockin_v2_app/core/utils/logger.dart';

/// Service for managing authentication token and user data persistence
class TokenStorage {
  static const String _tokenKey = 'auth_token';
  static const String _tokenTypeKey = 'auth_token_type';
  static const String _userDataKey = 'user_data';

  /// Save authentication data to local storage
  static Future<void> saveAuthData({
    required String token,
    required String tokenType,
    required User user,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await Future.wait([
        prefs.setString(_tokenKey, token),
        prefs.setString(_tokenTypeKey, tokenType),
        prefs.setString(_userDataKey, jsonEncode(user.toJson())),
      ]);
      
      AppLogger.i('Authentication data saved successfully');
    } catch (e, stackTrace) {
      AppLogger.e('Failed to save authentication data', e, stackTrace);
      rethrow;
    }
  }

  /// Retrieve stored authentication token
  static Future<String?> getToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_tokenKey);
    } catch (e, stackTrace) {
      AppLogger.e('Failed to retrieve token', e, stackTrace);
      return null;
    }
  }

  /// Retrieve stored token type
  static Future<String?> getTokenType() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_tokenTypeKey);
    } catch (e, stackTrace) {
      AppLogger.e('Failed to retrieve token type', e, stackTrace);
      return null;
    }
  }

  /// Retrieve stored user data
  static Future<User?> getUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userDataString = prefs.getString(_userDataKey);
      
      if (userDataString != null) {
        final userDataMap = jsonDecode(userDataString) as Map<String, dynamic>;
        return User.fromJson(userDataMap);
      }
      
      return null;
    } catch (e, stackTrace) {
      AppLogger.e('Failed to retrieve user data', e, stackTrace);
      return null;
    }
  }

  /// Check if user is authenticated (has valid token)
  static Future<bool> isAuthenticated() async {
    try {
      final token = await getToken();
      return token != null && token.isNotEmpty;
    } catch (e, stackTrace) {
      AppLogger.e('Failed to check authentication status', e, stackTrace);
      return false;
    }
  }

  /// Get complete authentication data
  static Future<AuthData?> getAuthData() async {
    try {
      final token = await getToken();
      final tokenType = await getTokenType();
      final user = await getUserData();
      
      if (token != null && tokenType != null && user != null) {
        return AuthData(
          token: token,
          tokenType: tokenType,
          user: user,
        );
      }
      
      return null;
    } catch (e, stackTrace) {
      AppLogger.e('Failed to retrieve authentication data', e, stackTrace);
      return null;
    }
  }

  /// Clear all authentication data
  static Future<void> clearAuthData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await Future.wait([
        prefs.remove(_tokenKey),
        prefs.remove(_tokenTypeKey),
        prefs.remove(_userDataKey),
      ]);
      
      AppLogger.i('Authentication data cleared successfully');
    } catch (e, stackTrace) {
      AppLogger.e('Failed to clear authentication data', e, stackTrace);
      rethrow;
    }
  }
}

/// Data class for complete authentication information
class AuthData {
  final String token;
  final String tokenType;
  final User user;

  const AuthData({
    required this.token,
    required this.tokenType,
    required this.user,
  });

  /// Get formatted authorization header value
  String get authorizationHeader => '$tokenType $token';
}
