import 'package:flutter_test/flutter_test.dart';
import 'package:flockin_v2_app/core/storage/token_storage.dart';
import 'package:flockin_v2_app/core/network/auth_models.dart';
import 'package:flockin_v2_app/presentation/bloc/auth/auth_bloc.dart';
import 'package:flockin_v2_app/presentation/bloc/auth/auth_event.dart';
import 'package:flockin_v2_app/presentation/bloc/auth/auth_state.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('Authentication Integration Tests', () {
    setUp(() async {
      // Clear any existing data before each test
      SharedPreferences.setMockInitialValues({});
      await TokenStorage.clearAuthData();
    });

    test('Token storage should save and retrieve auth data correctly', () async {
      // Create test user data
      final church = Church(id: '1', name: 'Test Church');
      final user = User(
        id: '123',
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user',
        church: church,
      );

      // Save auth data
      await TokenStorage.saveAuthData(
        token: 'test_token',
        tokenType: 'Bearer',
        user: user,
      );

      // Verify data was saved
      expect(await TokenStorage.isAuthenticated(), isTrue);
      
      final authData = await TokenStorage.getAuthData();
      expect(authData, isNotNull);
      expect(authData!.token, equals('test_token'));
      expect(authData.tokenType, equals('Bearer'));
      expect(authData.user.name, equals('Test User'));
      expect(authData.user.email, equals('<EMAIL>'));
      expect(authData.user.church.name, equals('Test Church'));
    });

    test('Token storage should clear auth data correctly', () async {
      // First save some data
      final church = Church(id: '1', name: 'Test Church');
      final user = User(
        id: '123',
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user',
        church: church,
      );

      await TokenStorage.saveAuthData(
        token: 'test_token',
        tokenType: 'Bearer',
        user: user,
      );

      // Verify data exists
      expect(await TokenStorage.isAuthenticated(), isTrue);

      // Clear data
      await TokenStorage.clearAuthData();

      // Verify data was cleared
      expect(await TokenStorage.isAuthenticated(), isFalse);
      expect(await TokenStorage.getAuthData(), isNull);
    });

    test('AuthBloc should handle authentication status check correctly', () async {
      final authBloc = AuthBloc();

      // Initially should be unauthenticated
      authBloc.add(const AuthStatusChecked());
      await expectLater(
        authBloc.stream,
        emits(const AuthUnauthenticated()),
      );

      // Save some auth data
      final church = Church(id: '1', name: 'Test Church');
      final user = User(
        id: '123',
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user',
        church: church,
      );

      await TokenStorage.saveAuthData(
        token: 'test_token',
        tokenType: 'Bearer',
        user: user,
      );

      // Check auth status again - should be authenticated
      authBloc.add(const AuthStatusChecked());
      await expectLater(
        authBloc.stream,
        emits(isA<AuthAuthenticated>()),
      );

      authBloc.close();
    });

    test('AuthData should generate correct authorization header', () {
      final church = Church(id: '1', name: 'Test Church');
      final user = User(
        id: '123',
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user',
        church: church,
      );

      final authData = AuthData(
        token: 'test_token_123',
        tokenType: 'Bearer',
        user: user,
      );

      expect(authData.authorizationHeader, equals('Bearer test_token_123'));
    });
  });
}
