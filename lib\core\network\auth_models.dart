class LoginRequest {
  final String email;
  final String password;

  LoginRequest({required this.email, required this.password});

  Map<String, dynamic> toJson() => {'email': email, 'password': password};
}

class Church {
  final String id;
  final String name;

  Church({required this.id, required this.name});

  factory Church.fromJson(Map<String, dynamic> json) =>
      Church(id: json['id'].toString(), name: json['name'].toString());

  Map<String, dynamic> toJson() => {'id': id, 'name': name};
}

class User {
  final String id;
  final String name;
  final String email;
  final String role;
  final Church church;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.role,
    required this.church,
  });

  factory User.fromJson(Map<String, dynamic> json) => User(
    id: json['id'].toString(),
    name: json['name'].toString(),
    email: json['email'].toString(),
    role: json['role'].toString(),
    church: Church.fromJson(json['church']),
  );

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'email': email,
    'role': role,
    'church': church.toJson(),
  };
}

class LoginResponse {
  final bool success;
  final String message;
  final User user;
  final String token;
  final String tokenType;

  LoginResponse({
    required this.success,
    required this.message,
    required this.user,
    required this.token,
    required this.tokenType,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) => LoginResponse(
    success: json['success'],
    message: json['message'],
    user: User.fromJson(json['data']['user']),
    token: json['data']['token'],
    tokenType: json['data']['token_type'],
  );
}
