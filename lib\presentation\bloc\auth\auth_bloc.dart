import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flockin_v2_app/core/network/auth_service.dart';
import 'package:flockin_v2_app/core/network/network_client.dart';
import 'package:flockin_v2_app/core/errors/app_exceptions.dart';
import 'package:flockin_v2_app/core/utils/logger.dart';
import 'auth_event.dart';
import 'auth_state.dart';

/// BLoC for managing authentication state
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthService _authService;

  AuthBloc({AuthService? authService})
    : _authService = authService ?? AuthService(NetworkClient()),
      super(const AuthInitial()) {
    on<LoginRequested>(_onLoginRequested);
    on<LogoutRequested>(_onLogoutRequested);
    on<AuthStatusChecked>(_onAuthStatusChecked);
  }

  /// Handle login request
  Future<void> _onLoginRequested(
    LoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    try {
      AppLogger.i('Login attempt for user: ${event.email}');
      final response = await _authService.login(event.email, event.password);

      AppLogger.i('Login successful for user: ${event.email}');
      emit(AuthAuthenticated(user: response.user, token: response.token));
    } catch (e) {
      AppLogger.e('Login failed for user: ${event.email}', e);

      String errorMessage = 'Login failed. Please try again.';
      if (e is AppException) {
        errorMessage = e.message;
      }

      emit(AuthError(message: errorMessage));
    }
  }

  /// Handle logout request
  Future<void> _onLogoutRequested(
    LogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      AppLogger.i('Logout requested');
      await _authService.logout();
      AppLogger.i('Logout successful');
      emit(const AuthUnauthenticated());
    } catch (e) {
      AppLogger.e('Logout failed', e);
      // Even if logout fails on server, clear local state
      emit(const AuthUnauthenticated());
    }
  }

  /// Handle checking authentication status
  Future<void> _onAuthStatusChecked(
    AuthStatusChecked event,
    Emitter<AuthState> emit,
  ) async {
    // For now, we'll assume user is unauthenticated on app start
    // This can be enhanced later to check stored tokens
    AppLogger.i('Checking authentication status');
    emit(const AuthUnauthenticated());
  }
}
