import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flockin_v2_app/core/network/auth_service.dart';
import 'package:flockin_v2_app/core/network/network_client.dart';
import 'package:flockin_v2_app/core/errors/app_exceptions.dart';
import 'package:flockin_v2_app/core/utils/logger.dart';
import 'package:flockin_v2_app/core/storage/token_storage.dart';
import 'auth_event.dart';
import 'auth_state.dart';

/// BLoC for managing authentication state
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthService _authService;

  AuthBloc({AuthService? authService})
    : _authService = authService ?? AuthService(NetworkClient()),
      super(const AuthInitial()) {
    on<LoginRequested>(_onLoginRequested);
    on<LogoutRequested>(_onLogoutRequested);
    on<AuthStatusChecked>(_onAuthStatusChecked);
  }

  /// Handle login request
  Future<void> _onLoginRequested(
    LoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    try {
      AppLogger.i('Login attempt for user: ${event.email}');
      final response = await _authService.login(event.email, event.password);

      // Save authentication data to storage
      await TokenStorage.saveAuthData(
        token: response.token,
        tokenType: response.tokenType,
        user: response.user,
      );

      AppLogger.i('Login successful for user: ${event.email}');
      emit(AuthAuthenticated(user: response.user, token: response.token));
    } catch (e) {
      AppLogger.e('Login failed for user: ${event.email}', e);

      String errorMessage = 'Login failed. Please try again.';
      if (e is AppException) {
        errorMessage = e.message;
      }

      emit(AuthError(message: errorMessage));
    }
  }

  /// Handle logout request
  Future<void> _onLogoutRequested(
    LogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    try {
      AppLogger.i('Logout requested');
      await _authService.logout();
      AppLogger.i('Logout API call successful');
    } catch (e) {
      AppLogger.e('Logout API call failed', e);
      // Continue with local logout even if API call fails
    }

    // Always clear local authentication data
    try {
      await TokenStorage.clearAuthData();
      AppLogger.i('Local authentication data cleared');
      emit(const AuthUnauthenticated());
    } catch (e) {
      AppLogger.e('Failed to clear local auth data', e);
      // Still emit unauthenticated state
      emit(const AuthUnauthenticated());
    }
  }

  /// Handle checking authentication status
  Future<void> _onAuthStatusChecked(
    AuthStatusChecked event,
    Emitter<AuthState> emit,
  ) async {
    AppLogger.i('Checking authentication status');

    try {
      final authData = await TokenStorage.getAuthData();

      if (authData != null) {
        AppLogger.i(
          'Found stored authentication data for user: ${authData.user.email}',
        );
        emit(AuthAuthenticated(user: authData.user, token: authData.token));
      } else {
        AppLogger.i('No stored authentication data found');
        emit(const AuthUnauthenticated());
      }
    } catch (e, stackTrace) {
      AppLogger.e('Error checking authentication status', e, stackTrace);
      // Clear potentially corrupted data and emit unauthenticated state
      await TokenStorage.clearAuthData();
      emit(const AuthUnauthenticated());
    }
  }
}
