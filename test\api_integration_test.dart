import 'package:flutter_test/flutter_test.dart';
import 'package:flockin_v2_app/core/network/network_client.dart';
import 'package:flockin_v2_app/core/network/auth_service.dart';
import 'package:flockin_v2_app/core/network/health_check_service.dart';

void main() {
  group('API Integration Tests', () {
    late NetworkClient networkClient;
    late AuthService authService;
    late HealthCheckService healthService;

    setUp(() {
      networkClient = NetworkClient();
      authService = AuthService(networkClient);
      healthService = HealthCheckService(networkClient);
    });

    test('Health check should return API status', () async {
      final result = await healthService.checkHealth();
      // Health check successful
      expect(result, isNotEmpty);
    });

    test('Login with provided credentials should work', () async {
      try {
        final response = await authService.login(
          '<EMAIL>',
          'asdf1234',
        );

        // Verify login response structure and data
        expect(response.success, isTrue);
        expect(response.user.email, equals('<EMAIL>'));
        expect(response.user.name, isNotEmpty);
        expect(response.user.role, isNotEmpty);
        expect(response.user.church.name, isNotEmpty);
        expect(response.token, isNotEmpty);
      } catch (e) {
        fail('Login should succeed with valid credentials: $e');
      }
    });

    test('Login with invalid credentials should fail', () async {
      try {
        await authService.login('<EMAIL>', 'wrongpassword');
        fail('Login should fail with invalid credentials');
      } catch (e) {
        // Expected failure for invalid credentials
        expect(e, isNotNull);
      }
    });
  });
}
