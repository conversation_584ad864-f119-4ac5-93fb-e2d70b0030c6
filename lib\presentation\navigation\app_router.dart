import 'package:go_router/go_router.dart';
import 'package:flockin_v2_app/presentation/pages/auth/login_page.dart';
import 'package:flockin_v2_app/presentation/pages/home/<USER>';
import 'package:flockin_v2_app/presentation/pages/media/media_page.dart';
import 'package:flockin_v2_app/presentation/pages/attendance/attendances_page.dart';
import 'package:flockin_v2_app/presentation/pages/profile/profile_page.dart';
import 'package:flockin_v2_app/presentation/pages/announcements/announcements_page.dart';
import 'package:flockin_v2_app/presentation/widgets/common/main_shell.dart';
import 'package:flockin_v2_app/core/storage/token_storage.dart';

/// App routing configuration using go_router with ShellRoute
class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/login',
    redirect: (context, state) async {
      final isAuthenticated = await TokenStorage.isAuthenticated();
      final isLoginRoute = state.matchedLocation == '/login';

      // If user is not authenticated and trying to access protected routes
      if (!isAuthenticated && !isLoginRoute) {
        return '/login';
      }

      // If user is authenticated and trying to access login page
      if (isAuthenticated && isLoginRoute) {
        return '/home';
      }

      // No redirect needed
      return null;
    },
    routes: [
      // Login Route (outside shell)
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),

      ShellRoute(
        builder: (context, state, child) => MainShell(child: child),
        routes: [
          GoRoute(
            path: '/home',
            name: 'home',
            builder: (context, state) => const HomePage(),
          ),
          GoRoute(
            path: '/media',
            name: 'media',
            builder: (context, state) => const MediaPage(),
          ),
          GoRoute(
            path: '/attendances',
            name: 'attendances',
            builder: (context, state) => const AttendancesPage(),
          ),
          GoRoute(
            path: '/profile',
            name: 'profile',
            builder: (context, state) => const ProfilePage(),
          ),
          GoRoute(
            path: '/announcements-list',
            name: 'announcements',
            builder: (context, state) => const AnnouncementsPage(),
          ),
        ],
      ),
    ],
  );
}
