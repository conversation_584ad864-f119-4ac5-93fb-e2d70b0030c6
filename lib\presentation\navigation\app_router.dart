import 'package:go_router/go_router.dart';
import 'package:flockin_v2_app/presentation/pages/auth/login_page.dart';
import 'package:flockin_v2_app/presentation/pages/home/<USER>';
import 'package:flockin_v2_app/presentation/pages/media/media_page.dart';
import 'package:flockin_v2_app/presentation/pages/attendance/attendances_page.dart';
import 'package:flockin_v2_app/presentation/pages/profile/profile_page.dart';
import 'package:flockin_v2_app/presentation/pages/announcements/announcements_page.dart';
import 'package:flockin_v2_app/presentation/widgets/common/main_shell.dart';

/// App routing configuration using go_router with ShellRoute
class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/login',
    routes: [
      // Login Route (outside shell)
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),

      ShellRoute(
        builder: (context, state, child) => MainShell(child: child),
        routes: [
          GoRoute(
            path: '/home',
            name: 'home',
            builder: (context, state) => const HomePage(),
          ),
          GoRoute(
            path: '/media',
            name: 'media',
            builder: (context, state) => const MediaPage(),
          ),
          GoRoute(
            path: '/attendances',
            name: 'attendances',
            builder: (context, state) => const AttendancesPage(),
          ),
          GoRoute(
            path: '/profile',
            name: 'profile',
            builder: (context, state) => const ProfilePage(),
          ),
          GoRoute(
            path: '/announcements-list',
            name: 'announcements',
            builder: (context, state) => const AnnouncementsPage(),
          ),
        ],
      ),
    ],
  );
}
