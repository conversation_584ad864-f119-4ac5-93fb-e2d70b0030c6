---
type: "manual"
---

# Flutter Development Guidelines (Summarized)

## Core Principles

- **I am the pilot, you are the co-pilot.** Follow my rules exactly.
- **Do not touch unrelated code.** Work only in the relevant files.
- Use **latest stable Flutter (3.24+)** & **Dart (3.4+)**.
- Follow **Material Design 3** & our **theming setup**.
- Use **pub.dev best practices** only.
- Commit work in branches — nothing else.

## Dependencies

- **State Management**: `flutter_bloc` (preferred) or `provider`
- **Navigation**: `go_router`
- **HTTP**: `dio` (+ interceptors)
- **Storage**: `shared_preferences`, `hive`, `sqflite`
- **DI**: `get_it`
- **Serialization**: `json_serializable`
- **Config**: `flutter_dotenv`
- **Media**: `cached_network_image`, `video_player`
- **Logging**: `logger` (use AppLogger utility class)

## Code Conventions

- Match existing codebase style.
- **Naming**:

  - Classes → `PascalCase`
  - Methods/vars → `camelCase`
  - Files → `snake_case`
  - Constants → `lowerCamelCase`

- **Imports**: Always use package imports for internal files (`package:flockin_v2_app/...`), never relative imports (`../`).
- **Check for reusable widgets** before creating new.
- Descriptive names (`isUserRegistered`, not `userNotif`).

## Architecture & Structure

```
lib/
├── core/ (constants, errors, network, theme, utils)
├── data/ (datasources, models, repositories)
├── domain/ (entities, repositories, usecases)
├── presentation/ (bloc/providers, pages, widgets, theme)
└── main.dart
```

- **Clean Architecture** (data/domain/presentation)
- **BLoC Pattern** for state mgmt
- **Repository Pattern** for data access
- **get_it** for DI
- **Atomic Design** for widgets (atoms, molecules, organisms)

## Development Practices

- Use **BLoC** for state (events, states, blocs).
- Use **GoRouter** for navigation.
- Handle errors with **custom exceptions**.
- Use `json_serializable` for models.
- Prefer **StatelessWidget**, use Stateful only if necessary.
- Business logic → BLoC/Provider, not widgets.
- Build **responsive UIs** (`LayoutBuilder`, breakpoints).

## UI/UX & Theming

- Always use **Material 3** + project theme.
- Reuse custom components (buttons, inputs, cards).
- Use **const constructors** where possible.
- Widgets → small, composable, single responsibility.

## Performance

- Use `ListView.builder` for large lists.
- Dispose controllers/streams properly.
- Use `CachedNetworkImage` with placeholders.
- Apply `RepaintBoundary` for heavy widgets.
- Provide `keys` in lists.

## Testing

- **Unit tests** for blocs, repos, usecases.
- **Widget tests** for UI.
- **Integration tests** for full flows.
- Mock APIs & services in tests.

## Security

- No plain text sensitive data.
- Use **Flutter Secure Storage** for tokens.
- Validate all inputs.
- Add auth headers via `dio` interceptors.
- Support certificate pinning where possible.

## Code Quality

- Run `flutter analyze` + `dart format`.
- Follow `flutter_lints`.
- Pre-commit hooks for formatting.
- Write doc comments (`///`) for public APIs.

## Version Control

- Branch per feature.
- Meaningful commit messages.
- Semantic versioning for releases.

## Deployment

- Configs via `AppConfig` + `flutter_dotenv`.
- Check performance (`--profile`).
- Minimal permissions only.
- Test on multiple devices & sizes.
- Update version numbers + release notes.

## Debugging & Monitoring

- Use **Flutter Inspector** + **DevTools**.
- Use **Crashlytics** for crash reporting.
- Log meaningfully using **AppLogger** (never use `print` statements in production code).
- Add analytics (respecting privacy).

⚡️ **Goal**: Clean, maintainable, themed, Material 3 apps — nothing outside these rules.
