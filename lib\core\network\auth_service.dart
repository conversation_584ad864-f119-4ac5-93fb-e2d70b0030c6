import 'package:dio/dio.dart';
import 'package:flockin_v2_app/core/network/network_client.dart';
import 'package:flockin_v2_app/core/errors/app_exceptions.dart';
import 'package:flockin_v2_app/core/utils/logger.dart';
import 'auth_models.dart';

class AuthService {
  final NetworkClient _client;

  AuthService(this._client);

  Future<LoginResponse> login(String email, String password) async {
    try {
      final request = LoginRequest(email: email, password: password);
      final response = await _client.post(
        '/auth/login',
        data: request.toJson(),
      );
      AppLogger.i('Login successful for user: $email');
      return LoginResponse.fromJson(response.data);
    } on DioException catch (e, stackTrace) {
      AppLogger.e('Login failed for user: $email', e, stackTrace);

      // Handle different types of errors with user-friendly messages
      if (e.response?.statusCode == 422) {
        // Validation error - usually invalid credentials
        throw const AuthException(
          'Invalid email or password. Please check your credentials and try again.',
        );
      } else if (e.response?.statusCode == 401) {
        // Unauthorized
        throw const AuthException('Invalid email or password.');
      } else if (e.response?.statusCode == 404) {
        // User not found
        throw const AuthException(
          'Account not found. Please check your email address.',
        );
      } else if (e.response?.statusCode == 429) {
        // Too many requests
        throw const AuthException(
          'Too many login attempts. Please try again later.',
        );
      } else if (e.response?.statusCode == 500) {
        // Server error
        throw const ServerException(
          'Server error occurred. Please try again later.',
        );
      } else if (e.type == DioExceptionType.connectionTimeout ||
          e.type == DioExceptionType.receiveTimeout) {
        // Timeout errors
        throw const NetworkException(
          'Connection timeout. Please check your internet connection.',
        );
      } else if (e.type == DioExceptionType.connectionError) {
        // Connection errors
        throw const NetworkException(
          'Unable to connect to server. Please check your internet connection.',
        );
      } else {
        // Generic error
        throw const AuthException('Login failed. Please try again.');
      }
    } catch (e, stackTrace) {
      AppLogger.e('Unexpected error during login', e, stackTrace);
      throw const AuthException(
        'An unexpected error occurred. Please try again.',
      );
    }
  }

  Future<void> logout() async {
    try {
      await _client.post('/auth/logout');
      AppLogger.i('User logged out successfully');
    } on DioException catch (e, stackTrace) {
      AppLogger.e('Logout failed', e, stackTrace);

      // Handle different types of errors with user-friendly messages
      if (e.response?.statusCode == 401) {
        // Already logged out or token expired - this is actually fine
        AppLogger.i('User was already logged out');
        return;
      } else if (e.response?.statusCode == 500) {
        // Server error
        throw const ServerException(
          'Server error occurred during logout. Please try again.',
        );
      } else if (e.type == DioExceptionType.connectionTimeout ||
          e.type == DioExceptionType.receiveTimeout) {
        // Timeout errors
        throw const NetworkException(
          'Connection timeout during logout. Please check your internet connection.',
        );
      } else if (e.type == DioExceptionType.connectionError) {
        // Connection errors
        throw const NetworkException(
          'Unable to connect to server during logout. Please check your internet connection.',
        );
      } else {
        // Generic error - but don't prevent logout on client side
        AppLogger.w(
          'Logout request failed but proceeding with client-side logout',
        );
        return;
      }
    } catch (e, stackTrace) {
      AppLogger.e('Unexpected error during logout', e, stackTrace);
      // Don't prevent logout on client side for unexpected errors
      return;
    }
  }
}
