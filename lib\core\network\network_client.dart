import 'package:dio/dio.dart';
import 'package:flockin_v2_app/core/storage/token_storage.dart';
import 'package:flockin_v2_app/core/utils/logger.dart';

class NetworkClient {
  static const String baseUrl =
      'https://flockin-v2-main-izu5wq.laravel.cloud/api';

  final Dio dio;

  NetworkClient()
    : dio = Dio(
        BaseOptions(
          baseUrl: baseUrl,
          connectTimeout: const Duration(seconds: 15),
          receiveTimeout: const Duration(seconds: 15),
          headers: {'Accept': 'application/json'},
        ),
      ) {
    // Add interceptor for authentication headers
    dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // Add authentication header if token is available
          final authData = await TokenStorage.getAuthData();
          if (authData != null) {
            options.headers['Authorization'] = authData.authorizationHeader;
            AppLogger.d('Added auth header to request: ${options.path}');
          }
          handler.next(options);
        },
        onError: (error, handler) async {
          // Handle 401 unauthorized errors by clearing stored auth data
          if (error.response?.statusCode == 401) {
            AppLogger.w('Received 401 error, clearing stored auth data');
            await TokenStorage.clearAuthData();
          }
          handler.next(error);
        },
      ),
    );
  }

  Future<Response> get(
    String path, {
    Map<String, dynamic>? queryParameters,
  }) async {
    return await dio.get(path, queryParameters: queryParameters);
  }

  Future<Response> post(
    String path, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
  }) async {
    return await dio.post(path, data: data, queryParameters: queryParameters);
  }

  // Add other HTTP methods as needed (put, delete, etc.)
}
